<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八下历史周过关4(7-8课) - 重点标注版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .question {
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
            padding-left: 20px;
            background: #f8f9ff;
            border-radius: 0 10px 10px 0;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .question:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .question-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #4a5568;
            margin-bottom: 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .question-title::before {
            content: "Q";
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }

        .answer {
            margin-left: 45px;
        }

        .answer-item {
            margin-bottom: 10px;
            padding: 10px 15px;
            border-radius: 8px;
            background: white;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .answer-item:hover {
            background: #edf2f7;
            border-color: #667eea;
        }

        /* 重点标注样式 */
        .key-point {
            background: linear-gradient(120deg, #ffd700 0%, #ffed4e 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: #744210;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }

        .important-date {
            background: linear-gradient(120deg, #ff6b6b 0%, #ff8e8e 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
        }

        .concept {
            background: linear-gradient(120deg, #4ecdc4 0%, #7bdcb5 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: #2d5a5a;
            box-shadow: 0 2px 4px rgba(78, 205, 196, 0.3);
        }

        .location {
            background: linear-gradient(120deg, #a8e6cf 0%, #c8f7c5 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: #2d5a2d;
            box-shadow: 0 2px 4px rgba(168, 230, 207, 0.3);
        }

        .significance {
            background: linear-gradient(120deg, #dda0dd 0%, #e6b3e6 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: #5a2d5a;
            box-shadow: 0 2px 4px rgba(221, 160, 221, 0.3);
        }

        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .legend {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 2px dashed #cbd5e0;
        }

        .legend h3 {
            margin-bottom: 15px;
            color: #4a5568;
        }

        .legend-item {
            display: inline-block;
            margin: 5px 10px;
        }

        @media print {
            body {
                background: white;
                padding: 0;
                margin: 0;
                font-size: 14px;
                line-height: 1.4;
            }
            .container {
                box-shadow: none;
                border-radius: 0;
                margin: 0;
                padding: 0;
                max-width: 100%;
            }
            .header {
                background: white !important;
                color: black !important;
                padding: 10px !important;
                margin-bottom: 5px;
                border-bottom: 2px solid #333;
            }
            .header h1 {
                font-size: 2em !important;
                margin-bottom: 8px !important;
            }
            .header p {
                font-size: 1.1em;
                margin: 0;
            }
            .content {
                padding: 10px !important;
            }
            .legend {
                padding: 8px !important;
                margin-bottom: 10px !important;
                font-size: 10px;
                border: 1px solid #ccc;
            }
            .legend h3 {
                font-size: 12px;
                margin-bottom: 5px !important;
            }
            .legend-item {
                margin: 2px 5px !important;
                font-size: 9px;
            }
            .question {
                margin-bottom: 8px !important;
                padding: 8px !important;
                border-left: 3px solid #333;
                background: white !important;
                break-inside: avoid;
                page-break-inside: avoid;
            }
            .question-title {
                font-size: 15px !important;
                margin-bottom: 8px !important;
                font-weight: bold;
            }
            .question-title::before {
                width: 22px !important;
                height: 22px !important;
                font-size: 12px !important;
                margin-right: 10px !important;
                background: #333 !important;
                color: white !important;
            }
            .answer {
                margin-left: 32px !important;
            }
            .answer-item {
                margin-bottom: 5px !important;
                padding: 5px 8px !important;
                font-size: 14px;
                line-height: 1.4;
                background: white !important;
                border: none;
            }
            .controls {
                display: none;
            }
            .print-tip {
                display: none !important;
            }
            .legend {
                display: none !important;
            }
            /* 确保标注颜色在打印时显示 */
            .key-point {
                background: #ffd700 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                padding: 2px 4px !important;
                font-size: 14px !important;
            }
            .important-date {
                background: #ff6b6b !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                padding: 2px 4px !important;
                font-size: 14px !important;
            }
            .concept {
                background: #4ecdc4 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                padding: 2px 4px !important;
                font-size: 14px !important;
            }
            .location {
                background: #a8e6cf !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                padding: 2px 4px !important;
                font-size: 14px !important;
            }
            .significance {
                background: #dda0dd !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                padding: 2px 4px !important;
                font-size: 14px !important;
            }
            /* 强制显示所有答案 */
            .answer {
                display: block !important;
            }
            /* 紧凑布局 */
            * {
                margin: 0 !important;
                padding: 0 !important;
            }
            .question {
                margin-bottom: 6px !important;
                padding: 6px !important;
            }
            .answer-item {
                margin-bottom: 2px !important;
                padding: 2px 4px !important;
            }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            .header {
                padding: 20px;
            }
            .header h1 {
                font-size: 2em;
            }
            .content {
                padding: 20px;
            }
            .controls {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="controls">
        <button class="btn" onclick="toggleHighlights()">切换标注</button>
        <button class="btn" onclick="printOptimized()">A4打印</button>
        <button class="btn" onclick="exportNotes()">导出笔记</button>
        <button class="btn" onclick="toggleAllAnswers()">展开全部</button>
    </div>

    <div class="container">
        <div class="header">
            <h1>八下历史周过关4(7-8课)</h1>
            <p>重点标注版 - 产品经理视角记忆法</p>
        </div>

        <div class="content">
            <div class="print-tip" style="background: #e8f4fd; border: 2px solid #3182ce; border-radius: 8px; padding: 15px; margin-bottom: 20px; text-align: center;">
                <h4 style="color: #2c5282; margin-bottom: 8px;">📄 A4打印优化提示</h4>
                <p style="color: #2d3748; font-size: 14px; margin: 0;">
                    点击"A4打印"按钮可自动展开所有内容并优化为A4纸张格式。
                    <br>建议打印前先点击"展开全部"预览完整内容。
                </p>
            </div>

            <div class="legend">
                <h3>📚 标注图例</h3>
                <div class="legend-item"><span class="key-point">核心概念</span></div>
                <div class="legend-item"><span class="important-date">重要时间</span></div>
                <div class="legend-item"><span class="concept">关键制度</span></div>
                <div class="legend-item"><span class="location">地点人物</span></div>
                <div class="legend-item"><span class="significance">历史意义</span></div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    1. <span class="key-point">十一届三中全会</span>召开的背景是什么?
                </div>
                <div class="answer">
                    <div class="answer-item">①人们要求纠正<span class="concept">文革</span>的错误</div>
                    <div class="answer-item">②"<span class="concept">两个凡是</span>"方针的推行引起普遍不满</div>
                    <div class="answer-item">③<span class="important-date">1978年</span>，思想届展开了一场<span class="key-point">真理标准的大讨论</span></div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    2. <span class="key-point">十一届三中全会</span>召开的时间、地点分别是什么?
                </div>
                <div class="answer">
                    <div class="answer-item"><span class="important-date">1978年</span>, <span class="location">北京</span></div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    3. <span class="key-point">十一届三中全会</span>的主要内容有哪些?
                </div>
                <div class="answer">
                    <div class="answer-item">①<strong>思想上</strong>：冲破了长期"左"的错误的严重束缚，确定了<span class="concept">解放思想、开动脑筋、实事求是、团结一致向前看</span>的指导方针。</div>
                    <div class="answer-item">②<strong>政治上</strong>：果断结束"<span class="concept">以阶级斗争为纲</span>"，重新确立马克思主义的思想路线、政治路线、组织路线，做出把党和国家工作中心转移到<span class="key-point">经济建设</span>上来，实行<span class="key-point">改革开放</span>的历史性决策。</div>
                    <div class="answer-item">③<strong>组织上</strong>：形成了以<span class="location">邓小平</span>为核心的党的<span class="concept">第二代中央领导集体</span>。</div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    4. <span class="key-point">十一届三中全会</span>召开的历史意义是什么?
                </div>
                <div class="answer">
                    <div class="answer-item">①是新中国成立以来党的历史上具有<span class="significance">深远意义的伟大转折</span>。</div>
                    <div class="answer-item">②开启了<span class="significance">改革开放和社会主义现代化建设新时期</span>。</div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    5. <span class="key-point">十一届三中全会</span>召开后的<span class="concept">拨乱反正</span>有哪些成果?
                </div>
                <div class="answer">
                    <div class="answer-item">①<strong>政治上</strong>：<span class="concept">平反冤假错案</span></div>
                    <div class="answer-item">②<strong>思想上</strong>：召开<span class="concept">中共十一届六中全会</span></div>
                    <div class="answer-item">③<strong>教育上</strong>：<span class="concept">恢复高考制度</span> (<span class="important-date">1977年</span>)</div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    6. 中华人民共和国成立以来最大的冤假错案得到平反是什么时候?
                </div>
                <div class="answer">
                    <div class="answer-item"><span class="important-date">1980年</span></div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    7. 标志着中国共产党在思想上的<span class="concept">拨乱反正</span>胜利完成的标志是什么?
                </div>
                <div class="answer">
                    <div class="answer-item"><span class="important-date">1981年</span><span class="concept">中共十一届六中全会</span>，通过了《<span class="key-point">关于建国以来党的若干历史问题的决议</span>》</div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    8. 我国<span class="key-point">经济体制改革</span>的方向、过程、目标、实质分别是什么?
                </div>
                <div class="answer">
                    <div class="answer-item"><strong>改革方向</strong>：从<span class="concept">计划经济</span>过渡到<span class="concept">社会主义市场经济</span></div>
                    <div class="answer-item"><strong>改革过程</strong>：从<span class="location">农村</span>到<span class="location">城市</span></div>
                    <div class="answer-item"><strong>改革目标</strong>：建立<span class="concept">社会主义市场经济体制</span></div>
                    <div class="answer-item"><strong>改革实质</strong>：<span class="significance">社会主义制度的自我完善和发展</span></div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    9. 我国的<span class="key-point">改革开放</span>从什么时候开始?从哪里开始?
                </div>
                <div class="answer">
                    <div class="answer-item"><span class="important-date">1978年</span>, <span class="location">农村</span></div>
                </div>
            </div>

            <div class="question">
                <div class="question-title" onclick="toggleAnswer(this)">
                    10. 我国农村经济体制改革，在农村实行什么制度?典型例子在哪里?主要内容是什么?有什么作用?
                </div>
                <div class="answer">
                    <div class="answer-item"><strong>制度</strong>：<span class="concept">家庭联产承包责任制</span></div>
                    <div class="answer-item"><strong>典型</strong>：<span class="location">安徽凤阳小岗村</span></div>
                    <div class="answer-item"><strong>主要内容</strong>：<span class="concept">分田包干到户，自负盈亏</span></div>
                    <div class="answer-item"><strong>作用</strong>：<span class="significance">激发了农民的劳动热情，带来了农村生产力的大解放，很大提高了农业生产和农民收入</span>。</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let highlightsVisible = true;

        function toggleHighlights() {
            const highlights = document.querySelectorAll('.key-point, .important-date, .concept, .location, .significance');
            highlightsVisible = !highlightsVisible;
            
            highlights.forEach(element => {
                if (highlightsVisible) {
                    element.style.background = element.className.includes('key-point') ? 'linear-gradient(120deg, #ffd700 0%, #ffed4e 100%)' :
                                            element.className.includes('important-date') ? 'linear-gradient(120deg, #ff6b6b 0%, #ff8e8e 100%)' :
                                            element.className.includes('concept') ? 'linear-gradient(120deg, #4ecdc4 0%, #7bdcb5 100%)' :
                                            element.className.includes('location') ? 'linear-gradient(120deg, #a8e6cf 0%, #c8f7c5 100%)' :
                                            'linear-gradient(120deg, #dda0dd 0%, #e6b3e6 100%)';
                } else {
                    element.style.background = 'transparent';
                }
            });
        }

        function toggleAnswer(element) {
            const answer = element.nextElementSibling;
            if (answer.style.display === 'none') {
                answer.style.display = 'block';
                answer.style.animation = 'fadeIn 0.3s ease';
            } else {
                answer.style.display = 'none';
            }
        }

        function printOptimized() {
            // 展开所有答案
            const answers = document.querySelectorAll('.answer');
            answers.forEach(answer => {
                answer.style.display = 'block';
            });

            // 确保标注可见
            if (!highlightsVisible) {
                toggleHighlights();
            }

            // 打印
            window.print();
        }

        function toggleAllAnswers() {
            const answers = document.querySelectorAll('.answer');
            const isAnyHidden = Array.from(answers).some(answer => answer.style.display === 'none');

            answers.forEach(answer => {
                answer.style.display = isAnyHidden ? 'block' : 'none';
            });
        }

        function exportNotes() {
            const keyPoints = [];
            document.querySelectorAll('.key-point').forEach(el => {
                keyPoints.push(el.textContent);
            });

            const dates = [];
            document.querySelectorAll('.important-date').forEach(el => {
                dates.push(el.textContent);
            });

            const concepts = [];
            document.querySelectorAll('.concept').forEach(el => {
                concepts.push(el.textContent);
            });

            const notes = `
重点知识点总结：
================

核心概念：
${keyPoints.join('\n')}

重要时间：
${dates.join('\n')}

关键制度：
${concepts.join('\n')}
            `;

            const blob = new Blob([notes], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '八下历史周过关4_重点笔记.txt';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 添加淡入动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为每个问题添加编号动画
            const questions = document.querySelectorAll('.question');
            questions.forEach((question, index) => {
                question.style.animationDelay = `${index * 0.1}s`;
                question.style.animation = 'fadeIn 0.5s ease forwards';
            });
        });
    </script>
</body>
</html>
