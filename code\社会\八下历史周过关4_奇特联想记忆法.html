<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八下历史周过关4 - 奇特心想联想记忆法</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .memory-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            color: white;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }

        .memory-card:hover {
            transform: translateY(-5px);
        }

        .question-num {
            background: rgba(255,255,255,0.2);
            display: inline-block;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .original-question {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #fff;
        }

        .original-answer {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #ffd700;
            font-size: 0.95em;
        }

        .original-answer h4 {
            color: #ffd700;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .answer-point {
            margin: 8px 0;
            padding-left: 15px;
            position: relative;
        }

        .answer-point::before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #ffd700;
        }

        .memory-story {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 20px;
            border-radius: 12px;
            margin: 15px 0;
            color: #333;
            border-left: 5px solid #ff6b6b;
        }

        .story-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .story-title::before {
            content: "🧠";
            margin-right: 10px;
            font-size: 1.5em;
        }

        .key-elements {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }

        .element {
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .association-chain {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 2px dashed #ff6b6b;
        }

        .chain-step {
            display: flex;
            align-items: center;
            margin: 8px 0;
            font-weight: bold;
            color: #2c3e50;
        }

        .chain-step::before {
            content: "→";
            color: #ff6b6b;
            font-size: 1.5em;
            margin-right: 10px;
        }

        .chain-step:first-child::before {
            content: "🎯";
        }

        .memory-tip {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            color: #8b4513;
            border-left: 4px solid #ff8c00;
        }

        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        @media print {
            body {
                background: white;
                padding: 0;
                font-size: 12px;
            }
            .container {
                box-shadow: none;
                border-radius: 0;
            }
            .controls {
                display: none;
            }
            .memory-card {
                break-inside: avoid;
                page-break-inside: avoid;
                background: white !important;
                color: black !important;
                border: 2px solid #333;
            }
            .memory-story {
                background: #f0f0f0 !important;
                color: black !important;
            }
            .association-chain {
                background: #f5f5f5 !important;
            }
            .memory-tip {
                background: #f9f9f9 !important;
                color: black !important;
            }
            .header {
                background: white !important;
                color: black !important;
                border-bottom: 3px solid #333;
            }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            .header {
                padding: 20px;
            }
            .content {
                padding: 20px;
            }
            .controls {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="controls">
        <button class="btn" onclick="window.print()">🖨️ 打印记忆卡</button>
        <button class="btn" onclick="toggleStories()">👁️ 切换故事</button>
        <button class="btn" onclick="randomReview()">🎲 随机复习</button>
        <button class="btn" onclick="startQuiz()">🧠 记忆测试</button>
        <button class="btn" onclick="showAllTips()">💡 显示口诀</button>
    </div>

    <div class="container">
        <div class="header">
            <h1>🧠 八下历史周过关4(7-8课)</h1>
            <p>奇特心想联想记忆法 - 让历史活起来</p>
        </div>

        <div class="content">
            <!-- 记忆卡片1 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 1</div>
                <div class="original-question">
                    <strong>原题：</strong>十一届三中全会召开的背景是什么？
                </div>

                <div class="original-answer">
                    <h4>📚 标准答案：</h4>
                    <div class="answer-point">人们要求纠正文革的错误</div>
                    <div class="answer-point">"两个凡是"方针的推行引起普遍不满</div>
                    <div class="answer-point">1978年，思想界展开了一场真理标准的大讨论</div>
                </div>

                <div class="memory-story">
                    <div class="story-title">🎭 背景故事：三个愤怒的人</div>
                    <p><strong>想象场景：</strong>1978年，有三个愤怒的人在北京街头抗议...</p>

                    <div class="association-chain">
                        <div class="chain-step">第一个人举着"文革错误"的牌子，脸上写满愤怒</div>
                        <div class="chain-step">第二个人高喊"两个凡是太死板！"，手里挥舞着报纸</div>
                        <div class="chain-step">第三个人是个哲学家，大声讨论"什么是真理？"</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">🔥 文革愤怒</span>
                    <span class="element">📰 两个凡是</span>
                    <span class="element">🤔 真理讨论</span>
                    <span class="element">📅 1978年</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"三个愤怒的人在78年闹事：文革错、凡是烦、真理辩"
                </div>
            </div>

            <!-- 记忆卡片2 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 2</div>
                <div class="original-question">
                    <strong>原题：</strong>十一届三中全会的时间、地点？
                </div>

                <div class="original-answer">
                    <h4>📚 标准答案：</h4>
                    <div class="answer-point">时间：1978年12月</div>
                    <div class="answer-point">地点：北京</div>
                </div>

                <div class="memory-story">
                    <div class="story-title">🏛️ 时空定位：北京78年的冬天</div>
                    <p><strong>联想记忆：</strong>想象一个巨大的"78"数字雕塑立在北京天安门前，雕塑上刻着"十一届三中全会"...</p>

                    <div class="association-chain">
                        <div class="chain-step">"78"像两个人在拥抱（改革开放拥抱新时代）</div>
                        <div class="chain-step">北京的"京"字像一座宫殿（会议地点）</div>
                        <div class="chain-step">十二月是冬天，三中全会在冬天召开</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">🗓️ 1978年12月</span>
                    <span class="element">🏛️ 北京</span>
                    <span class="element">❄️ 冬季</span>
                    <span class="element">🤝 拥抱变化</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"78年北京冬，十一三中会议隆"
                </div>
            </div>

            <!-- 记忆卡片3 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 3</div>
                <div class="original-question">
                    <strong>原题：</strong>十一届三中全会的主要内容？
                </div>

                <div class="original-answer">
                    <h4>📚 标准答案：</h4>
                    <div class="answer-point">思想路线：重新确立解放思想、实事求是的思想路线</div>
                    <div class="answer-point">政治路线：停止使用"以阶级斗争为纲"的口号，把党和国家的工作重心转移到经济建设上来</div>
                    <div class="answer-point">组织路线：形成了以邓小平为核心的党中央领导集体</div>
                    <div class="answer-point">重大决策：作出实行改革开放的伟大决策</div>
                </div>

                <div class="memory-story">
                    <div class="story-title">🎯 三把钥匙开三扇门</div>
                    <p><strong>奇特联想：</strong>邓小平手持三把神奇钥匙，分别开启思想、政治、组织三扇大门...</p>

                    <div class="association-chain">
                        <div class="chain-step">🧠 思想门：解放思想，实事求是（脑袋要灵活）</div>
                        <div class="chain-step">🏛️ 政治门：从斗争转向经济建设（不打架，要赚钱）</div>
                        <div class="chain-step">👥 组织门：邓小平成为核心（新船长上任）</div>
                        <div class="chain-step">🚪 决策门：改革开放大门敞开（走向世界）</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">🧠 解放思想</span>
                    <span class="element">💰 经济中心</span>
                    <span class="element">🚪 改革开放</span>
                    <span class="element">👨‍✈️ 邓小平核心</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"四把钥匙四扇门：思想解放、经济中心、邓公掌舵、改革开放"
                </div>
            </div>

            <!-- 记忆卡片4 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 4</div>
                <div class="original-question">
                    <strong>原题：</strong>十一届三中全会的历史意义？
                </div>

                <div class="original-answer">
                    <h4>📚 标准答案：</h4>
                    <div class="answer-point">是建国以来党的历史上具有深远意义的伟大转折</div>
                    <div class="answer-point">开启了改革开放和社会主义现代化建设的新时期</div>
                    <div class="answer-point">从此，中国历史进入社会主义现代化建设的新时期</div>
                </div>

                <div class="memory-story">
                    <div class="story-title">🌅 历史的转折点</div>
                    <p><strong>视觉联想：</strong>想象中国这艘大船在1978年遇到了历史的转折点，从此改变航向...</p>

                    <div class="association-chain">
                        <div class="chain-step">🚢 大船转向：从革命航线转向建设航线</div>
                        <div class="chain-step">🌅 新时代黎明：改革开放的太阳升起</div>
                        <div class="chain-step">🚀 现代化航程：驶向现代化的彼岸</div>
                        <div class="chain-step">🎯 深远影响：影响了几代中国人</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">🔄 伟大转折</span>
                    <span class="element">🌅 新时期</span>
                    <span class="element">🚀 现代化</span>
                    <span class="element">📈 改革开放</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"大船转向新时代，改革开放现代化"
                </div>
            </div>

            <!-- 记忆卡片5 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 5</div>
                <div class="original-question">
                    <strong>原题：</strong>拨乱反正的成果？
                </div>

                <div class="original-answer">
                    <h4>📚 标准答案：</h4>
                    <div class="answer-point">平反了大量的冤假错案</div>
                    <div class="answer-point">1981年党的十一届六中全会通过了《关于建国以来党的若干历史问题的决议》</div>
                    <div class="answer-point">1977年恢复高考制度</div>
                </div>

                <div class="memory-story">
                    <div class="story-title">🔧 三个修理工的故事</div>
                    <p><strong>形象比喻：</strong>想象三个修理工在修复被破坏的东西...</p>

                    <div class="association-chain">
                        <div class="chain-step">⚖️ 政治修理工：平反冤假错案（还清白）</div>
                        <div class="chain-step">🧠 思想修理工：召开六中全会（理清思路）</div>
                        <div class="chain-step">📚 教育修理工：恢复高考（77年重启）</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">⚖️ 平反冤案</span>
                    <span class="element">📋 六中全会</span>
                    <span class="element">📚 恢复高考</span>
                    <span class="element">🗓️ 1977年</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"三个修理工：政治平反、思想理清、教育重启"
                </div>
            </div>

            <!-- 记忆卡片6 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 6</div>
                <div class="original-question">
                    <strong>原题：</strong>改革开放的总设计师是谁？为什么这样称呼？
                </div>

                <div class="original-answer">
                    <h4>📚 标准答案：</h4>
                    <div class="answer-point">邓小平被称为改革开放的总设计师</div>
                    <div class="answer-point">因为他为改革开放事业作出了创造性的贡献</div>
                    <div class="answer-point">他提出了改革开放的基本方针政策</div>
                    <div class="answer-point">指导了改革开放的具体实践</div>
                </div>

                <div class="memory-story">
                    <div class="story-title">🏗️ 超级建筑师的故事</div>
                    <p><strong>形象联想：</strong>想象邓小平戴着建筑师的安全帽，手持巨大的设计图纸...</p>

                    <div class="association-chain">
                        <div class="chain-step">🏗️ 邓小平 = 超级建筑师（设计师帽子）</div>
                        <div class="chain-step">📐 设计图纸 = 改革开放蓝图</div>
                        <div class="chain-step">🏢 建造成果 = 现代化中国</div>
                        <div class="chain-step">👷 指导施工 = 具体实践指导</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">👨‍🔧 邓小平</span>
                    <span class="element">🏗️ 总设计师</span>
                    <span class="element">📐 改革蓝图</span>
                    <span class="element">🌟 伟大贡献</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"邓公戴帽画蓝图，改革开放总设计"
                </div>
            </div>

            <!-- 记忆卡片7 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 7</div>
                <div class="original-question">
                    <strong>原题：</strong>家庭联产承包责任制的特点和作用？
                </div>

                <div class="memory-story">
                    <div class="story-title">🏠 农民分家的故事</div>
                    <p><strong>生活联想：</strong>想象一个大家庭决定分家，每家分到自己的田地...</p>

                    <div class="association-chain">
                        <div class="chain-step">🏠 大家庭分家 = 人民公社解体</div>
                        <div class="chain-step">🌾 每家有自己的田 = 承包到户</div>
                        <div class="chain-step">💰 多劳多得 = 积极性提高</div>
                        <div class="chain-step">📈 粮食增产 = 温饱问题解决</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">🏠 承包到户</span>
                    <span class="element">🌾 自主经营</span>
                    <span class="element">💪 积极性高</span>
                    <span class="element">📈 粮食增产</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"农民分家有田地，多劳多得粮满仓"
                </div>
            </div>

            <!-- 记忆卡片8 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 8</div>
                <div class="original-question">
                    <strong>原题：</strong>经济特区的设立时间、地点和作用？
                </div>

                <div class="memory-story">
                    <div class="story-title">🏝️ 四个神奇岛屿</div>
                    <p><strong>地理联想：</strong>1980年，中国海边出现了四个神奇的岛屿...</p>

                    <div class="association-chain">
                        <div class="chain-step">🏝️ 深圳岛：紧邻香港的魔法岛</div>
                        <div class="chain-step">🏝️ 珠海岛：靠近澳门的宝石岛</div>
                        <div class="chain-step">🏝️ 汕头岛：潮汕文化的商贸岛</div>
                        <div class="chain-step">🏝️ 厦门岛：美丽的海上花园岛</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">📅 1980年</span>
                    <span class="element">🏙️ 深圳</span>
                    <span class="element">💎 珠海</span>
                    <span class="element">🌊 汕头</span>
                    <span class="element">🌺 厦门</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"80年四岛现：深珠汕厦特区建"
                </div>
            </div>

            <!-- 记忆卡片9 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 9</div>
                <div class="original-question">
                    <strong>原题：</strong>对外开放格局的形成过程？
                </div>

                <div class="memory-story">
                    <div class="story-title">🌊 海浪式开放</div>
                    <p><strong>波浪联想：</strong>想象中国的开放像海浪一样，一波接一波地推进...</p>

                    <div class="association-chain">
                        <div class="chain-step">🌊 第一波：经济特区（点的突破）</div>
                        <div class="chain-step">🌊 第二波：沿海开放城市（线的延伸）</div>
                        <div class="chain-step">🌊 第三波：沿海经济开放区（面的扩展）</div>
                        <div class="chain-step">🌊 第四波：内地开放（全面开花）</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">🎯 点：特区</span>
                    <span class="element">📏 线：沿海城市</span>
                    <span class="element">🗺️ 面：开放区</span>
                    <span class="element">🌍 全面开放</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"开放如海浪：点线面全方位"
                </div>
            </div>

            <!-- 记忆卡片10 -->
            <div class="memory-card">
                <div class="question-num">记忆卡片 10</div>
                <div class="original-question">
                    <strong>原题：</strong>改革开放的历史意义？
                </div>

                <div class="memory-story">
                    <div class="story-title">🚀 中国号火箭升空</div>
                    <p><strong>太空联想：</strong>改革开放就像给中国号火箭加了燃料，让它飞向现代化的太空...</p>

                    <div class="association-chain">
                        <div class="chain-step">🚀 火箭燃料 = 改革开放政策</div>
                        <div class="chain-step">🌍 脱离地球 = 摆脱贫困落后</div>
                        <div class="chain-step">🌟 飞向太空 = 走向现代化</div>
                        <div class="chain-step">🛰️ 成功着陆 = 全面小康社会</div>
                    </div>
                </div>

                <div class="key-elements">
                    <span class="element">💪 强国之路</span>
                    <span class="element">🌟 富民之路</span>
                    <span class="element">🚀 现代化</span>
                    <span class="element">🌍 融入世界</span>
                </div>

                <div class="memory-tip">
                    <strong>💡 记忆口诀：</strong>"改革开放火箭燃，强国富民奔小康"
                </div>
            </div>
        </div>
    </div>

    <script>
        let storiesVisible = true;
        let tipsVisible = false;

        function toggleStories() {
            const stories = document.querySelectorAll('.memory-story');
            storiesVisible = !storiesVisible;

            stories.forEach(story => {
                story.style.display = storiesVisible ? 'block' : 'none';
            });

            const btn = event.target;
            btn.textContent = storiesVisible ? '👁️ 隐藏故事' : '👁️ 显示故事';
        }

        function randomReview() {
            const cards = document.querySelectorAll('.memory-card');
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            randomCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
            randomCard.style.transform = 'scale(1.05)';
            randomCard.style.boxShadow = '0 15px 40px rgba(255,107,107,0.4)';
            setTimeout(() => {
                randomCard.style.transform = 'scale(1)';
                randomCard.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            }, 1500);
        }

        function showAllTips() {
            const tips = document.querySelectorAll('.memory-tip');
            tipsVisible = !tipsVisible;

            tips.forEach((tip, index) => {
                setTimeout(() => {
                    tip.style.display = tipsVisible ? 'block' : 'none';
                    if (tipsVisible) {
                        tip.style.animation = 'pulse 0.6s ease';
                    }
                }, index * 100);
            });

            const btn = event.target;
            btn.textContent = tipsVisible ? '💡 隐藏口诀' : '💡 显示口诀';
        }

        function startQuiz() {
            const questions = [
                "十一届三中全会召开的时间是？",
                "改革开放的总设计师是谁？",
                "第一批经济特区有哪四个？",
                "家庭联产承包责任制的特点是什么？",
                "对外开放格局是如何形成的？"
            ];

            const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
            alert(`🧠 记忆测试：\n\n${randomQuestion}\n\n请在心中回答，然后查看相关记忆卡片验证！`);
        }

        // 添加动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.memory-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 添加点击卡片翻转效果
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'rotateY(10deg) scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = 'rotateY(0deg) scale(1)';
                    }, 300);
                });
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .memory-card {
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .memory-card:active {
                transform: scale(0.98);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
